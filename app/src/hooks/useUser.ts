/**
 * Consolidated hooks for user/employee management operations
 * This replaces both useUser and useEmployee hooks
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { UserService } from '@/services/api/userService';
import { CreateUserRequest, UpdateUserRequest, LoginRequest } from '@/types/api/user';
import { QueryParams } from '@/types/api/common';

/**
 * Hook for fetching a paginated list of users/employees
 */
export const useUsers = (params?: QueryParams) => {
  return useQuery({
    queryKey: ['users', params],
    queryFn: () => UserService.getUsers(params),
  });
};

/**
 * Hook for fetching users/employees by department ID
 */
export const useUsersByDepartment = (departmentId: string | number, params?: QueryParams) => {
  const queryParams = {
    ...params,
    filter: {
      ...params?.filter,
      departmentId: departmentId
    }
  };

  return useQuery({
    queryKey: ['users', 'department', departmentId, params],
    queryFn: () => UserService.getUsers(queryParams),
    enabled: !!departmentId,
  });
};

/**
 * Hook for fetching a single user/employee by ID
 */
export const useUser = (id: string | number) => {
  return useQuery({
    queryKey: ['user', id],
    queryFn: () => UserService.getUserById(id),
    enabled: !!id,
  });
};

/**
 * Hook for creating a new user/employee
 */
export const useCreateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateUserRequest) => UserService.createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
};

/**
 * Hook for updating an existing user/employee
 */
export const useUpdateUser = (id: string | number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateUserRequest) => UserService.updateUser(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['user', id] });
    },
  });
};

/**
 * Hook for deleting a user/employee
 */
export const useDeleteUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string | number) => UserService.deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
};

/**
 * Hook for enabling a user/employee
 */
export const useEnableUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string | number) => UserService.enableUser(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['user', id] });
    },
  });
};

/**
 * Hook for disabling a user/employee
 */
export const useDisableUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string | number) => UserService.disableUser(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['user', id] });
    },
  });
};

/**
 * Hook for searching users/employees
 */
export const useSearchUsers = (searchTerm?: string, role?: string, enabled?: boolean, params?: QueryParams) => {
  return useQuery({
    queryKey: ['users', 'search', searchTerm, role, enabled, params],
    queryFn: () => UserService.searchUsers(searchTerm, role, enabled, params),
    enabled: !!(searchTerm || role !== undefined || enabled !== undefined),
  });
};

/**
 * Hook for terminating a user/employee
 */
export const useTerminateUser = (id: string | number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (terminationDate: string) =>
      UserService.updateUser(id, {
        enabled: false,
        terminationDate: terminationDate
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['user', id] });
    },
  });
};

/**
 * Hook for user login
 */
export const useLogin = () => {
  return useMutation({
    mutationFn: (data: LoginRequest) => UserService.login(data),
    onSuccess: (data) => {
      localStorage.setItem('auth_token', data.data.token);
    },
  });
};

/**
 * Hook for user logout
 */
export const useLogout = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: UserService.logout,
    onSuccess: () => {
      queryClient.clear();
    },
  });
};

// Legacy aliases for backward compatibility
export const useEmployees = useUsers;
export const useEmployee = useUser;
export const useCreateEmployee = useCreateUser;
export const useUpdateEmployee = useUpdateUser;
export const useDeleteEmployee = useDeleteUser;