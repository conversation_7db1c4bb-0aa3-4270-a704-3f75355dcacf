import { apiClient } from './client';
import {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  LoginRequest,
  LoginResponse,
} from '@/types/api/user';
import { ApiResponse, PaginatedResponse, QueryParams } from '@/types/api/common';

/**
 * Service for user/employee management operations
 * Uses backend /api/v1/users endpoints
 */
export class UserService {
  private static BASE_URL = '/api/v1/users';

  /**
   * Get a paginated list of users/employees
   */
  static async getUsers(params?: QueryParams): Promise<PaginatedResponse<User>> {
    try {
      return await apiClient.get<PaginatedResponse<User>>(this.BASE_URL, { params });
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  }

  /**
   * Get a single user/employee by ID
   */
  static async getUserById(id: string | number): Promise<ApiResponse<User>> {
    try {
      return await apiClient.get<ApiResponse<User>>(`${this.BASE_URL}/${id}`);
    } catch (error) {
      console.error('Error fetching user:', error);
      throw error;
    }
  }

  /**
   * Create a new user/employee
   */
  static async createUser(data: CreateUserRequest): Promise<ApiResponse<User>> {
    try {
      return await apiClient.post<ApiResponse<User>>(this.BASE_URL, data);
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Update an existing user/employee
   */
  static async updateUser(id: string | number, data: UpdateUserRequest): Promise<ApiResponse<User>> {
    try {
      return await apiClient.put<ApiResponse<User>>(`${this.BASE_URL}/${id}`, data);
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Delete a user/employee
   */
  static async deleteUser(id: string | number): Promise<ApiResponse<void>> {
    try {
      return await apiClient.delete<ApiResponse<void>>(`${this.BASE_URL}/${id}`);
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  /**
   * Enable a user/employee
   */
  static async enableUser(id: string | number): Promise<ApiResponse<User>> {
    try {
      return await apiClient.patch<ApiResponse<User>>(`${this.BASE_URL}/${id}/enable`);
    } catch (error) {
      console.error('Error enabling user:', error);
      throw error;
    }
  }

  /**
   * Disable a user/employee
   */
  static async disableUser(id: string | number): Promise<ApiResponse<User>> {
    try {
      return await apiClient.patch<ApiResponse<User>>(`${this.BASE_URL}/${id}/disable`);
    } catch (error) {
      console.error('Error disabling user:', error);
      throw error;
    }
  }

  /**
   * Search users/employees
   */
  static async searchUsers(searchTerm?: string, role?: string, enabled?: boolean, params?: QueryParams): Promise<PaginatedResponse<User>> {
    try {
      const searchParams = {
        ...params,
        searchTerm,
        role,
        enabled,
      };
      return await apiClient.get<PaginatedResponse<User>>(`${this.BASE_URL}/search`, { params: searchParams });
    } catch (error) {
      console.error('Error searching users:', error);
      throw error;
    }
  }

  /**
   * Login user
   */
  static async login(data: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    try {
      return await apiClient.post<ApiResponse<LoginResponse>>('/auth/login', data);
    } catch (error) {
      console.error('Error logging in:', error);
      throw error;
    }
  }

  /**
   * Logout user
   */
  static async logout(): Promise<void> {
    try {
      localStorage.removeItem('auth_token');
    } catch (error) {
      console.error('Error logging out:', error);
      throw error;
    }
  }
}