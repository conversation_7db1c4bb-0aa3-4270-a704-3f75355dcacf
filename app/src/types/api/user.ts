/**
 * User interface aligned with backend User entity
 * This consolidates both user and employee data into a single entity
 */
export interface User {
  // Core user fields
  id: number;
  email: string;
  name: string;
  role: Role;
  enabled: boolean;
  phoneNumber: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;

  // Employee-related fields
  organizationId?: number;
  organizationName?: string;
  departmentId?: number;
  departmentName?: string;
  designationId?: number;
  designationName?: string;
  employeeId?: string;
  joiningDate?: string;
  terminationDate?: string;
  reportingManagerId?: number;
  reportingManagerName?: string;
}

/**
 * Role enum matching backend Role enum
 */
export enum Role {
  ROLE_USER = 'ROLE_USER',
  ROLE_ADMIN = 'ROLE_ADMIN',
  ROLE_SUPER_ADMIN = 'ROLE_SUPER_ADMIN',
}

/**
 * Legacy UserRole enum for backward compatibility
 * @deprecated Use Role instead
 */
export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER',
  GUEST = 'GUEST',
}

/**
 * Request for creating a new user/employee
 * Matches backend UserCreateRequest
 */
export interface CreateUserRequest {
  email: string;
  password: string;
  name: string;
  role: Role;
  phoneNumber: string;
  organizationId: number;
  departmentId: number;
  designationId: number;
  // Optional employee fields
  employeeId?: string;
  joiningDate?: string;
  reportingManagerId?: number;
}

/**
 * Request for updating an existing user/employee
 * Matches backend UserUpdateRequest
 */
export interface UpdateUserRequest {
  email?: string;
  password?: string;
  name?: string;
  role?: Role;
  phoneNumber?: string;
  organizationId?: number;
  departmentId?: number;
  designationId?: number;
  enabled?: boolean;
  // Optional employee fields
  employeeId?: string;
  joiningDate?: string;
  terminationDate?: string;
  reportingManagerId?: number;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: User;
}