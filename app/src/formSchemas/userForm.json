{"fields": [{"type": "text", "name": "email", "label": "Email", "placeholder": "Enter email address", "icon": "pi-envelope", "validation": {"required": true, "email": true, "maxLength": 100}}, {"type": "password", "name": "password", "label": "Password", "placeholder": "Enter password (min 8 chars, must include uppercase, lowercase, number, special char)", "icon": "pi-lock", "validation": {"required": true, "minLength": 8, "maxLength": 100}}, {"type": "text", "name": "name", "label": "Full Name", "placeholder": "Enter full name", "icon": "pi-user", "validation": {"required": true, "maxLength": 50}}, {"type": "select", "name": "role", "label": "Role", "placeholder": "Select role", "icon": "pi-id-card", "options": [], "validation": {"required": true}}, {"type": "text", "name": "phoneNumber", "label": "Phone Number", "placeholder": "Enter phone number", "icon": "pi-phone", "validation": {"required": true, "maxLength": 20}}, {"type": "select", "name": "organizationId", "label": "Organization", "placeholder": "Select organization", "icon": "pi-sitemap", "options": [], "validation": {"required": true}}, {"type": "select", "name": "departmentId", "label": "Department", "placeholder": "Select department", "icon": "pi-building", "options": [], "validation": {"required": true}}, {"type": "select", "name": "designationId", "label": "Designation", "placeholder": "Select designation", "icon": "pi-briefcase", "options": [], "validation": {"required": true}}, {"type": "text", "name": "employeeId", "label": "Employee ID", "placeholder": "Enter employee ID (optional)", "icon": "pi-id-card", "validation": {"required": false}}, {"type": "date", "name": "joiningDate", "label": "Joining Date", "placeholder": "Select joining date", "icon": "pi-calendar", "validation": {"required": false}}, {"type": "date", "name": "terminationDate", "label": "Termination Date", "placeholder": "Select termination date (if applicable)", "dateRestriction": {"range": "future", "includeToday": false}, "icon": "pi-calendar-times", "validation": {"required": false}}, {"type": "select", "name": "reportingManagerId", "label": "Reporting Manager", "placeholder": "Select reporting manager (optional)", "icon": "pi-user-plus", "options": [], "validation": {"required": false}}, {"type": "checkbox", "name": "enabled", "label": "Enabled", "defaultValue": true, "validation": {"required": false}}], "actions": [{"id": "submit", "type": "submit", "label": "Add User"}, {"id": "cancel", "type": "button", "label": "Cancel"}]}