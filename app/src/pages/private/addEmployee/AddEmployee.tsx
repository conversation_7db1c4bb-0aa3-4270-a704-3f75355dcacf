import React, { useState, useRef, useMemo } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { DynamicForm } from '@/components/form/DynamicForm';
import userFormSchemaJson from '@/formSchemas/userForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import { useCreateUser, useUsers } from '@/hooks/useUser';
import { CreateUserRequest, Role } from '@/types/api/user';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import Modal from '@/components/ui/Modal/Modal';
import { getEnumValues } from '@/utils/enumUtils';
import { employeeDetailsRoute } from '@/routes/private/employeeDetails.route';
import { useOrganizationsShort } from '@/hooks/useOrganization';
import { useDepartments } from '@/hooks/useDepartment';
import { useDesignations } from '@/hooks/useDesignation';
import './AddEmployee.css';

// Helper function to safely format date values
const formatDateValue = (dateValue: any): string => {
    if (!dateValue) return '';

    if (dateValue instanceof Date) {
        return dateValue.toLocaleDateString();
    }

    if (typeof dateValue === 'string') {
        // If it's already a string, return it
        return dateValue;
    }

    // Fallback: convert to string
    return String(dateValue);
};

const AddEmployee: React.FC = () => {
    const navigate = useNavigate();
    const toast = useRef<ToastRef>(null);
    const [error, setError] = useState<string | null>(null);
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const [userData, setUserData] = useState<CreateUserRequest | null>(null);
    const createUserMutation = useCreateUser();

    // Fetch data for dropdowns
    const { data: organizationsData } = useOrganizationsShort();
    const { data: departmentsData } = useDepartments();
    const { data: designationsData } = useDesignations();
    const { data: usersData } = useUsers(); // For reporting manager dropdown

    // Get enum values
    const roleValues = useMemo(() => getEnumValues(Role), []);

    // Create a dynamic form schema with the enum values
    const userFormSchema = useMemo(() => {
        // Clone the original schema
        const schema = JSON.parse(JSON.stringify(userFormSchemaJson)) as FormSchema;

        // Find the role field and update its options
        const roleField = schema.fields.find(field => field.name === 'role');
        if (roleField && roleField.type === 'select') {
            roleField.options = roleValues.map(role => ({
                label: role.replace('ROLE_', '').replace('_', ' '),
                value: role
            }));
        }

        // Update organization options
        const organizationField = schema.fields.find(field => field.name === 'organizationId');
        if (organizationField && organizationField.type === 'select' && organizationsData) {
            organizationField.options = organizationsData.map((org: any) => ({
                label: org.name,
                value: org.id.toString()
            }));
        }

        // Update department options
        const departmentField = schema.fields.find(field => field.name === 'departmentId');
        if (departmentField && departmentField.type === 'select' && departmentsData) {
            departmentField.options = departmentsData.map((dept: any) => ({
                label: dept.name,
                value: dept.id?.toString() || dept.id
            }));
        }

        // Update designation options
        const designationField = schema.fields.find(field => field.name === 'designationId');
        if (designationField && designationField.type === 'select' && designationsData?.data) {
            designationField.options = designationsData.data.map((designation: any) => ({
                label: designation.name,
                value: designation.id
            }));
        }

        // Update reporting manager options
        const reportingManagerField = schema.fields.find(field => field.name === 'reportingManagerId');
        if (reportingManagerField && reportingManagerField.type === 'select' && usersData?.data) {
            reportingManagerField.options = usersData.data.map((user: any) => ({
                label: user.name,
                value: user.id.toString()
            }));
        }

        return schema;
    }, [roleValues, organizationsData, departmentsData, designationsData, usersData]);

    const handleSubmit = async (data: any) => {
        try {
            setError(null);

            // Format the data to match our CreateUserRequest type
            const formattedData: CreateUserRequest = {
                email: data.email,
                password: data.password,
                name: data.name,
                role: data.role as Role,
                phoneNumber: data.phoneNumber,
                organizationId: parseInt(data.organizationId),
                departmentId: parseInt(data.departmentId),
                designationId: parseInt(data.designationId),
                // Optional employee fields
                employeeId: data.employeeId || undefined,
                joiningDate: data.joiningDate ? formatDateValue(data.joiningDate) : undefined,
                reportingManagerId: data.reportingManagerId ? parseInt(data.reportingManagerId) : undefined,
            };

            await createUserMutation.mutateAsync(formattedData);

            // Show success message
            toast.current?.showSuccess('User/Employee successfully added to the system');

            // Store user data for display in modal
            setUserData(formattedData);

            // Show modal with user information
            setIsModalOpen(true);
        } catch (err: any) {
            console.error('Error adding user/employee:', err);
            const errorMessage = err.response?.data?.message || 'Failed to add user/employee. Please try again.';
            setError(errorMessage);
        }
    };

    // Function to handle modal close and navigate to employee details
    const handleModalClose = () => {
        setIsModalOpen(false);
        navigate({ to: employeeDetailsRoute.to });
    };

    // Function to handle cancel button click
    const handleCancel = () => {
        navigate({ to: employeeDetailsRoute.to });
    };

    // Function to format user/employee data for display
    const formatUserInfo = () => {
        if (!userData) return null;

        return (
            <div className="user-summary p-3">
                <h3 className="text-lg font-semibold mb-3">User/Employee Information Summary</h3>

                <div className="grid grid-cols-2 gap-3">
                    <div className="col">
                        <h4 className="font-medium text-gray-700">Personal Information</h4>
                        <div className="field-group mt-2">
                            <p><span className="font-medium">Name:</span> {userData.name}</p>
                            <p><span className="font-medium">Email:</span> {userData.email}</p>
                            <p><span className="font-medium">Phone:</span> {userData.phoneNumber}</p>
                            <p><span className="font-medium">Role:</span> {userData.role.replace('ROLE_', '').replace('_', ' ')}</p>
                        </div>
                    </div>

                    <div className="col">
                        <h4 className="font-medium text-gray-700">Employment Details</h4>
                        <div className="field-group mt-2">
                            <p><span className="font-medium">Organization ID:</span> {userData.organizationId}</p>
                            <p><span className="font-medium">Department ID:</span> {userData.departmentId}</p>
                            <p><span className="font-medium">Designation ID:</span> {userData.designationId}</p>
                            {userData.employeeId && <p><span className="font-medium">Employee ID:</span> {userData.employeeId}</p>}
                            {userData.joiningDate && <p><span className="font-medium">Joining Date:</span> {userData.joiningDate}</p>}
                            {userData.reportingManagerId && <p><span className="font-medium">Reporting Manager ID:</span> {userData.reportingManagerId}</p>}
                        </div>
                    </div>
                </div>

                <div className="mt-4 text-center">
                    <p className="text-green-600 font-medium">
                        User/Employee has been successfully added to the system!
                    </p>
                </div>
            </div>
        );
    };

    return (
        <div className="p-4">
            <Toast ref={toast} position="top-right" />
            <Card
                title="Add Employee"
                subtitle="Enter employee details"
                variant="elevated"
                padding="large"
                className="max-w-3xl mx-auto"
            >
                <DynamicForm
                    schema={userFormSchema}
                    onSubmit={handleSubmit}
                    className="mt-4"
                    buttonHandlers={{
                        cancel: handleCancel
                    }}
                />
                {error && <div className="p-error mt-3 text-center">{error}</div>}
            </Card>

            {/* Modal to display user/employee information */}
            <Modal
                visible={isModalOpen}
                onHide={handleModalClose}
                header="User/Employee Added Successfully"
                footerButtons={[
                    {
                        label: "Okay",
                        onClick: handleModalClose,
                        variant: "primary"
                    }
                ]}
            >
                {formatUserInfo()}
            </Modal>
        </div>
    );
};

export default AddEmployee;