import { ColumnConfig, DataGrid } from "@/components/ui/DataGrid/DataGrid";
import { DataTableStateEvent } from "primereact/datatable";
import { useEffect, useState } from "react";
import '@pages/private/EmployeeDetails/EmployeeDetails.css';
import Button from "@/components/ui/Button/Button";
import Modal from "@/components/ui/Modal/Modal";
import Card from "@/components/ui/Card/Card";
import { useNavigate } from "@tanstack/react-router";
import { addEmployeeRoute } from "@/routes/private/addEmployee.route";
import { editEmployeeRoute } from "@/routes/private/editEmployee.route";
import { useUsers } from "@/hooks/useUser";
import { User } from "@/types/api/user";

const EmployeeDetails: React.FC = () => {
    const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentUser, setCurrentUser] = useState<User | null>(null);
    const [first, setFirst] = useState(0);
    const [rows, setRows] = useState(10);
    const [sortField, setSortField] = useState<string>('');
    const [sortOrder, setSortOrder] = useState<number>(1);
    const navigate = useNavigate();

    // Fetch users using the consolidated useUsers hook
    const { data: usersResponse, isLoading, error } = useUsers({
        page: Math.floor(first / rows),
        size: rows,
        sort: sortField ? `${sortField},${sortOrder === 1 ? 'asc' : 'desc'}` : undefined
    });

    // Extract users and total from response
    const users = usersResponse?.data || [];
    const totalRecords = usersResponse?.totalElements || 0;

    const handlePageChange = (event: { first: number; rows: number; page: number }) => {
        setFirst(event.first);
        setRows(event.rows);
    };

    const handleSort = (event: DataTableStateEvent) => {
        setSortField(event.sortField || '');
        setSortOrder(event.sortOrder || 1);
    };

    // Handlers for add, edit, delete
    const handleAdd = () => {
        navigate({ to: addEmployeeRoute.to });
    };

    const handleEdit = (user: User) => {
        // Navigate to the edit employee page
        // In a real implementation, you would pass the employee ID as a parameter
        console.log(`Editing user with ID: ${user.id}`);
        navigate({ to: editEmployeeRoute.to });
    };

    const handleDelete = () => {
        if (selectedUsers.length === 0) return;
        // TODO: Implement delete logic (e.g., show confirmation and remove user)
        alert(`Delete users: ${selectedUsers.map(user => user.name).join(', ')} (not implemented)`);
    };

    const columns: ColumnConfig[] = [
        {
            field: 'name',
            header: 'Name',
            sortable: true
        },
        {
            field: 'email',
            header: 'Email',
            sortable: true
        },
        {
            field: 'role',
            header: 'Role',
            sortable: true,
            body: (rowData: User) => (
                <span>{rowData.role?.replace('ROLE_', '').replace('_', ' ')}</span>
            )
        },
        {
            field: 'enabled',
            header: 'Status',
            sortable: true,
            body: (rowData: User) => (
                <span className={`status-badge ${rowData.enabled ? 'active' : 'inactive'}`}>
                    {rowData.enabled ? 'Active' : 'Inactive'}
                </span>
            )
        },
        {
            field: 'departmentName',
            header: 'Department',
            sortable: true
        },
        {
            field: 'designationName',
            header: 'Designation',
            sortable: true
        },
        {
            field: 'actions',
            header: 'Actions',
            sortable: false,
            body: (rowData: User) => (
                <div className="flex gap-2 justify-content-center">
                    <Button
                        icon="pi pi-pencil"
                        variant="outline"
                        size="small"
                        onClick={() => handleEdit(rowData)}
                        aria-label="Edit"
                    />
                </div>
            )
        }
    ];

    return (
        <div className="employee_details p-4">
            <Card title="Employee Details" variant="elevated" className="mb-4">
                <div className="flex justify-content-end gap-2 mb-3">
                    <Button variant="primary" size="small" onClick={handleAdd} disabled={selectedUsers.length > 0}>
                        Add
                    </Button>
                    <Button variant="danger" size="small" onClick={handleDelete} disabled={selectedUsers.length === 0}>
                        Delete
                    </Button>
                    {/* TODO: move the below configs out of the componenent */}
                    <Modal
                        visible={isModalOpen}
                        onHide={() => {
                            setIsModalOpen(false);
                            setCurrentUser(null);
                        }}
                        header="User Details"
                        children={
                            <>
                                {currentUser && (
                                    <div className="flex flex-column gap-3 p-fluid">
                                        <div className="flex flex-column gap-2">
                                            <label className="font-bold">Name</label>
                                            <div>{currentUser.name}</div>
                                        </div>
                                        <div className="flex flex-column gap-2">
                                            <label className="font-bold">Email</label>
                                            <div>{currentUser.email}</div>
                                        </div>
                                        <div className="flex flex-column gap-2">
                                            <label className="font-bold">Role</label>
                                            <div>{currentUser.role?.replace('ROLE_', '').replace('_', ' ')}</div>
                                        </div>
                                        <div className="flex flex-column gap-2">
                                            <label className="font-bold">Status</label>
                                            <div>
                                                <span className={`status-badge ${currentUser.enabled ? 'active' : 'inactive'}`}>
                                                    {currentUser.enabled ? 'Active' : 'Inactive'}
                                                </span>
                                            </div>
                                        </div>
                                        <div className="flex flex-column gap-2">
                                            <label className="font-bold">Department</label>
                                            <div>{currentUser.departmentName || 'N/A'}</div>
                                        </div>
                                        <div className="flex flex-column gap-2">
                                            <label className="font-bold">Designation</label>
                                            <div>{currentUser.designationName || 'N/A'}</div>
                                        </div>
                                        <div className="flex flex-column gap-2">
                                            <label className="font-bold">Employee ID</label>
                                            <div>{currentUser.employeeId || 'N/A'}</div>
                                        </div>
                                        <div className="flex flex-column gap-2">
                                            <label className="font-bold">Phone Number</label>
                                            <div>{currentUser.phoneNumber || 'N/A'}</div>
                                        </div>
                                    </div>
                                )}
                            </>
                        }
                        footerButtons={[
                            {
                                label: 'Close',
                                icon: 'pi pi-times',
                                onClick: () => {
                                    setIsModalOpen(false);
                                    setCurrentUser(null);
                                },
                                variant: 'outline'
                            }
                        ]} />
                </div>
                <DataGrid
                    value={users}
                    columns={columns}
                    totalRecords={totalRecords}
                    loading={isLoading}
                    onPage={handlePageChange}
                    onSort={handleSort}
                    rows={rows}
                    first={first}
                    rowsPerPageOptions={[10, 25, 50]}
                    showGridLines={true}
                    stripedRows={true}
                    selection={selectedUsers}
                    selectionMode="checkbox"
                    onSelectionChange={e => setSelectedUsers(Array.isArray(e.value) ? e.value : [e.value])}
                />
                {error && <div className="p-error mt-3 text-center">Error loading users: {error.message}</div>}
            </Card>
        </div>
    );
}

export default EmployeeDetails;