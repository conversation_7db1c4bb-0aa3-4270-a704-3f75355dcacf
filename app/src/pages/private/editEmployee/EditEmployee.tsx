import React, { useState, useRef, useMemo } from 'react';
import { useNavigate, useParams } from '@tanstack/react-router';
import { DynamicForm } from '@/components/form/DynamicForm';
import userFormSchemaJson from '@/formSchemas/userForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import { useUpdateUser, useUser, useUsers } from '@/hooks/useUser';
import { UpdateUserRequest, Role } from '@/types/api/user';
import Card from '@/components/ui/Card/Card';
import { employeeDetailsRoute } from '@/routes/private/employeeDetails.route';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import Modal from '@/components/ui/Modal/Modal';
import { getEnumValues } from '@/utils/enumUtils';
import { useOrganizationsShort } from '@/hooks/useOrganization';
import { useDepartments } from '@/hooks/useDepartment';
import { useDesignations } from '@/hooks/useDesignation';
import './EditEmployee.css';

// Helper function to safely format date values
const formatDateValue = (dateValue: any): string => {
    if (!dateValue) return '';

    if (dateValue instanceof Date) {
        return dateValue.toLocaleDateString();
    }

    if (typeof dateValue === 'string') {
        // If it's already a string, return it
        return dateValue;
    }

    // Fallback: convert to string
    return String(dateValue);
};

// Remove mock data - now using real API data

const EditEmployee: React.FC = () => {
    const navigate = useNavigate();
    const toast = useRef<ToastRef>(null);
    const [error, setError] = useState<string | null>(null);
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const [userData, setUserData] = useState<UpdateUserRequest | null>(null);

    // Get employee ID from route params
    const { employeeId } = useParams();

    // Fetch employee data using the consolidated useUser hook
    const { data: userResponse, isLoading: isUserLoading, error: userError } = useUser(employeeId);

    // Fetch data for dropdowns
    const { data: organizationsData } = useOrganizationsShort();
    const { data: departmentsData } = useDepartments();
    const { data: designationsData } = useDesignations();
    const { data: usersData } = useUsers(); // For reporting manager dropdown

    // Get update mutation
    const updateUserMutation = useUpdateUser(employeeId);

    // Get enum values
    const roleValues = useMemo(() => getEnumValues(Role), []);

    // Create a dynamic form schema with the enum values and modified submit button
    const userFormSchema = useMemo(() => {
        // Clone the original schema
        const schema = JSON.parse(JSON.stringify(userFormSchemaJson)) as FormSchema;

        // Find the role field and update its options
        const roleField = schema.fields.find(field => field.name === 'role');
        if (roleField && roleField.type === 'select') {
            roleField.options = roleValues.map(role => ({
                label: role.replace('ROLE_', '').replace('_', ' '),
                value: role
            }));
        }

        // Update organization options
        const organizationField = schema.fields.find(field => field.name === 'organizationId');
        if (organizationField && organizationField.type === 'select' && organizationsData) {
            organizationField.options = organizationsData.map((org: any) => ({
                label: org.name,
                value: org.id.toString()
            }));
        }

        // Update department options
        const departmentField = schema.fields.find(field => field.name === 'departmentId');
        if (departmentField && departmentField.type === 'select' && departmentsData) {
            departmentField.options = departmentsData.map((dept: any) => ({
                label: dept.name,
                value: dept.id?.toString() || dept.id
            }));
        }

        // Update designation options
        const designationField = schema.fields.find(field => field.name === 'designationId');
        if (designationField && designationField.type === 'select' && designationsData?.data) {
            designationField.options = designationsData.data.map((designation: any) => ({
                label: designation.name,
                value: designation.id
            }));
        }

        // Update reporting manager options
        const reportingManagerField = schema.fields.find(field => field.name === 'reportingManagerId');
        if (reportingManagerField && reportingManagerField.type === 'select' && usersData?.data) {
            reportingManagerField.options = usersData.data.map((user: any) => ({
                label: user.name,
                value: user.id.toString()
            }));
        }

        // Remove password field for edit form
        schema.fields = schema.fields.filter(field => field.name !== 'password');

        // Update the submit button label
        if (schema.actions && schema.actions.length > 0) {
            schema.actions[0].label = 'Save Changes';
        }

        return schema;
    }, [roleValues, organizationsData, departmentsData, designationsData, usersData]);

    // Prepare default values for the form from the real user/employee data
    const defaultValues = useMemo(() => {
        if (!userResponse?.data) return {};

        const user = userResponse.data;
        return {
            email: user.email,
            name: user.name,
            phoneNumber: user.phoneNumber,
            role: user.role,
            organizationId: user.organizationId?.toString(),
            departmentId: user.departmentId?.toString(),
            designationId: user.designationId?.toString(),
            employeeId: user.employeeId || '',
            joiningDate: user.joiningDate || '',
            terminationDate: user.terminationDate || '',
            reportingManagerId: user.reportingManagerId?.toString(),
            enabled: user.enabled
        };
    }, [userResponse]);

    const handleSubmit = async (data: any) => {
        try {
            setError(null);

            // Format the data to match our UpdateUserRequest type
            const formattedData: UpdateUserRequest = {
                email: data.email,
                name: data.name,
                role: data.role as Role,
                phoneNumber: data.phoneNumber,
                organizationId: data.organizationId ? parseInt(data.organizationId) : undefined,
                departmentId: data.departmentId ? parseInt(data.departmentId) : undefined,
                designationId: data.designationId ? parseInt(data.designationId) : undefined,
                enabled: data.enabled,
                // Optional employee fields
                employeeId: data.employeeId || undefined,
                joiningDate: data.joiningDate ? formatDateValue(data.joiningDate) : undefined,
                terminationDate: data.terminationDate ? formatDateValue(data.terminationDate) : undefined,
                reportingManagerId: data.reportingManagerId ? parseInt(data.reportingManagerId) : undefined,
            };

            await updateUserMutation.mutateAsync(formattedData);

            // Show success message
            toast.current?.showSuccess('User/Employee information successfully updated');

            // Store user data for display in modal
            setUserData(formattedData);

            // Show modal with user information
            setIsModalOpen(true);
        } catch (err: any) {
            console.error('Error updating user/employee:', err);
            const errorMessage = err.response?.data?.message || 'Failed to update user/employee. Please try again.';
            setError(errorMessage);
        }
    };

    // Function to handle modal close and navigate to employee details
    const handleModalClose = () => {
        setIsModalOpen(false);
        navigate({ to: employeeDetailsRoute.to });
    };

    // Function to handle cancel button click
    const handleCancel = () => {
        navigate({ to: employeeDetailsRoute.to });
    };

    // Function to format user/employee data for display
    const formatUserInfo = () => {
        if (!userData) return null;

        return (
            <div className="user-summary p-3">
                <h3 className="text-lg font-semibold mb-3">Updated User/Employee Information</h3>

                <div className="flex flex-wrap gap-3">
                    <div className="flex-1 min-w-[250px]">
                        <h4 className="font-medium text-gray-700">Personal Information</h4>
                        <div className="field-group mt-2">
                            <p><span className="font-medium">Name:</span> {userData.name}</p>
                            <p><span className="font-medium">Email:</span> {userData.email}</p>
                            <p><span className="font-medium">Phone:</span> {userData.phoneNumber}</p>
                            <p><span className="font-medium">Role:</span> {userData.role?.replace('ROLE_', '').replace('_', ' ')}</p>
                        </div>
                    </div>

                    <div className="flex-1 min-w-[250px]">
                        <h4 className="font-medium text-gray-700">Employment Details</h4>
                        <div className="field-group mt-2">
                            <p><span className="font-medium">Organization ID:</span> {userData.organizationId}</p>
                            <p><span className="font-medium">Department ID:</span> {userData.departmentId}</p>
                            <p><span className="font-medium">Designation ID:</span> {userData.designationId}</p>
                            {userData.employeeId && <p><span className="font-medium">Employee ID:</span> {userData.employeeId}</p>}
                            {userData.joiningDate && <p><span className="font-medium">Joining Date:</span> {userData.joiningDate}</p>}
                            {userData.terminationDate && <p><span className="font-medium">Termination Date:</span> {userData.terminationDate}</p>}
                            {userData.reportingManagerId && <p><span className="font-medium">Reporting Manager ID:</span> {userData.reportingManagerId}</p>}
                            <p><span className="font-medium">Status:</span> {userData.enabled ? 'Enabled' : 'Disabled'}</p>
                        </div>
                    </div>
                </div>

                <div className="mt-4 text-center">
                    <p className="text-green-600 font-medium">
                        User/Employee information has been successfully updated!
                    </p>
                </div>
            </div>
        );
    };

    return (
        <div className="p-4">
            <Toast ref={toast} position="top-right" />
            <Card
                title="Edit Employee"
                subtitle="Update employee details"
                variant="elevated"
                padding="large"
                className="max-w-3xl mx-auto"
            >
                <DynamicForm
                    schema={userFormSchema}
                    onSubmit={handleSubmit}
                    defaultValues={defaultValues}
                    className="mt-4"
                    buttonHandlers={{
                        cancel: handleCancel
                    }}
                />
                {error && <div className="p-error mt-3 text-center">{error}</div>}
                {isUserLoading && <div className="text-center mt-3">Loading user data...</div>}
                {userError && <div className="p-error mt-3 text-center">Error loading user data</div>}
            </Card>

            {/* Modal to display user/employee information */}
            <Modal
                visible={isModalOpen}
                onHide={handleModalClose}
                header="User/Employee Updated Successfully"
                footerButtons={[
                    {
                        label: "Okay",
                        onClick: handleModalClose,
                        variant: "primary"
                    }
                ]}
            >
                {formatUserInfo()}
            </Modal>
        </div>
    );
};

export default EditEmployee;
